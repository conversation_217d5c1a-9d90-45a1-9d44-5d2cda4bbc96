'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { SystemSettings } from '@/lib/types';
import { 
  Save, 
  Download, 
  Upload, 
  RotateCw, 
  Eye,
  Palette,
  Type,
  Layout,
  FileImage,
  Settings
} from 'lucide-react';

interface ReportLayoutSettings {
  logo: {
    size: number;
    position: 'left' | 'center' | 'right';
    marginTop: number;
    marginBottom: number;
    marginLeft: number;
    marginRight: number;
  };
  title: {
    fontSize: number;
    fontWeight: 'normal' | 'bold' | 'bolder';
    color: string;
    position: 'left' | 'center' | 'right';
    marginTop: number;
    marginBottom: number;
  };
  companyInfo: {
    fontSize: number;
    fontWeight: 'normal' | 'bold';
    color: string;
    position: 'left' | 'center' | 'right';
    marginTop: number;
    marginBottom: number;
    showArabic: boolean;
    showEnglish: boolean;
  };
  table: {
    headerBackgroundColor: string;
    headerTextColor: string;
    rowAlternateColor: string;
    borderColor: string;
    fontSize: number;
    cellPadding: number;
  };
  footer: {
    fontSize: number;
    color: string;
    position: 'left' | 'center' | 'right';
    marginTop: number;
    showPageNumbers: boolean;
    showDate: boolean;
  };
  page: {
    marginTop: number;
    marginBottom: number;
    marginLeft: number;
    marginRight: number;
    orientation: 'portrait' | 'landscape';
  };
}

interface ReportPreviewDesignerProps {
  settings: SystemSettings;
  onSave: (layout: NonNullable<SystemSettings['reportLayout']>) => void;
}

const defaultLayout: ReportLayoutSettings = {
  logo: {
    size: 60,
    position: 'right',
    marginTop: 10,
    marginBottom: 15,
    marginLeft: 15,
    marginRight: 15,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    position: 'center',
    marginTop: 10,
    marginBottom: 15,
  },
  companyInfo: {
    fontSize: 12,
    fontWeight: 'normal',
    color: '#666666',
    position: 'right',
    marginTop: 5,
    marginBottom: 20,
    showArabic: true,
    showEnglish: false,
  },
  table: {
    headerBackgroundColor: '#2c3e50',
    headerTextColor: '#ffffff',
    rowAlternateColor: '#f8f9fa',
    borderColor: '#dee2e6',
    fontSize: 11,
    cellPadding: 8,
  },
  footer: {
    fontSize: 10,
    color: '#666666',
    position: 'center',
    marginTop: 20,
    showPageNumbers: true,
    showDate: true,
  },
  page: {
    marginTop: 20,
    marginBottom: 20,
    marginLeft: 20,
    marginRight: 20,
    orientation: 'portrait',
  },
};

export function ReportPreviewDesigner({ settings, onSave }: ReportPreviewDesignerProps) {
  const [layout, setLayout] = useState<ReportLayoutSettings>(
    settings.reportLayout || defaultLayout
  );
  const [activeTab, setActiveTab] = useState('preview');
  const [previewMode, setPreviewMode] = useState<'sales' | 'supply' | 'stocktake' | 'custom'>('sales');
  const previewRef = useRef<HTMLDivElement>(null);

  const updateLayout = (section: keyof ReportLayoutSettings, updates: any) => {
    setLayout(prev => ({
      ...prev,
      [section]: { ...prev[section], ...updates }
    }));
  };

  const resetToDefaults = () => {
    setLayout(defaultLayout);
  };

  const exportLayout = () => {
    const blob = new Blob([JSON.stringify(layout, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'report-layout.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  const importLayout = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedLayout = JSON.parse(e.target?.result as string);
          setLayout(importedLayout);
        } catch (error) {
          console.error('خطأ في استيراد التخطيط:', error);
        }
      };
      reader.readAsText(file);
    }
  };

  // بيانات تجريبية مختلفة حسب نوع التقرير
  const getPreviewData = () => {
    switch (previewMode) {
      case 'sales':
        return {
          title: 'فاتورة بيع',
          subtitle: 'رقم الفاتورة: SO-2024-001',
          headers: ['الرقم التسلسلي', 'الموديل', 'الحالة', 'السعر'],
          data: [
            ['354123456789012', 'iPhone 14 Pro', 'جديد', '3,500.00 ر.س'],
            ['354987654321098', 'Samsung Galaxy S23', 'مستعمل', '2,800.00 ر.س'],
            ['354456789123456', 'iPad Air', 'جديد', '2,200.00 ر.س'],
          ],
          totals: { total: '8,500.00 ر.س', count: 3 }
        };
      case 'supply':
        return {
          title: 'أمر توريد',
          subtitle: 'رقم الأمر: PO-2024-001',
          headers: ['الرقم التسلسلي', 'الموديل', 'المورد', 'تكلفة الشراء'],
          data: [
            ['354111111111111', 'iPhone 15 Pro', 'مورد الأجهزة', '3,200.00 ر.س'],
            ['354222222222222', 'MacBook Pro', 'شركة التقنية', '8,500.00 ر.س'],
            ['354333333333333', 'AirPods Pro', 'متجر الأجهزة', '899.00 ر.س'],
          ],
          totals: { total: '12,599.00 ر.س', count: 3 }
        };
      case 'stocktake':
        return {
          title: 'تقرير جرد المخزون',
          subtitle: 'تاريخ الجرد: ' + new Date().toLocaleDateString('ar-SA'),
          headers: ['الموديل', 'العدد المتوقع', 'العدد الفعلي', 'الفرق'],
          data: [
            ['iPhone 14 Pro', '25', '23', '-2'],
            ['Samsung Galaxy S23', '30', '32', '+2'],
            ['iPad Air', '15', '15', '0'],
          ],
          totals: { total: '70 جهاز', count: 3 }
        };
      default:
        return {
          title: 'تقرير مخصص',
          subtitle: 'معاينة التصميم',
          headers: ['العمود الأول', 'العمود الثاني', 'العمود الثالث', 'العمود الرابع'],
          data: [
            ['بيانات تجريبية 1', 'قيمة 1', 'معلومة 1', 'نتيجة 1'],
            ['بيانات تجريبية 2', 'قيمة 2', 'معلومة 2', 'نتيجة 2'],
            ['بيانات تجريبية 3', 'قيمة 3', 'معلومة 3', 'نتيجة 3'],
          ],
          totals: { total: 'إجمالي', count: 3 }
        };
    }
  };

  const previewData = getPreviewData();

  const PreviewComponent = () => (
    <div
      ref={previewRef}
      className="bg-white border shadow-lg mx-auto relative"
      style={{
        width: layout.page.orientation === 'portrait' ? '210mm' : '297mm',
        minHeight: layout.page.orientation === 'portrait' ? '297mm' : '210mm',
        padding: `${layout.page.marginTop}px ${layout.page.marginRight}px ${layout.page.marginBottom}px ${layout.page.marginLeft}px`,
        transform: 'scale(0.6)',
        transformOrigin: 'top center',
        direction: 'rtl',
      }}
    >
      {/* الشعار في المنتصف */}
      {settings.logoUrl && (
        <div style={{ textAlign: 'center', margin: `${layout.logo.marginTop}px 0 ${layout.logo.marginBottom}px 0` }}>
          <img
            src={settings.logoUrl}
            alt="Logo"
            style={{ width: layout.logo.size, height: layout.logo.size, objectFit: 'contain', display: 'inline-block' }}
          />
        </div>
      )}

      {/* اسم الشركة بخط كبير بارز */}
      <div style={{ textAlign: 'center', fontSize: layout.title.fontSize + 10, fontWeight: 'bold', color: layout.title.color, margin: '0 0 10px 0' }}>
        {settings.companyNameAr}
      </div>

      {/* العنوان الرئيسي: عربي يمين، انجليزي يسار */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 10 }}>
        <div style={{ textAlign: 'right', fontSize: layout.title.fontSize, fontWeight: layout.title.fontWeight, color: layout.title.color, flex: 1 }}>{previewData.title}</div>
        <div style={{ textAlign: 'left', fontSize: layout.title.fontSize, fontWeight: layout.title.fontWeight, color: layout.title.color, flex: 1, direction: 'ltr' }}>{previewData.title === 'فاتورة بيع' ? 'Sales Invoice' : previewData.title === 'أمر توريد' ? 'Supply Order' : previewData.title === 'تقرير جرد المخزون' ? 'Stocktake Report' : 'Custom Report'}</div>
      </div>

      {/* نوع التقرير مسطر */}
      <div style={{ textAlign: 'center', fontSize: layout.title.fontSize * 0.9, fontWeight: 'bold', color: '#007bff', borderBottom: '2px solid #007bff', marginBottom: 10, paddingBottom: 4 }}>
        {previewData.subtitle}
      </div>

      {/* سطر معلومات (عميل/مورد/مخزن/رقم الأمر/موظف/تاريخ) */}
      <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: layout.companyInfo.fontSize + 2, marginBottom: 10 }}>
        <div style={{ textAlign: 'right', flex: 1 }}>
          {previewMode === 'sales' && 'العميل: محمد صالح'}
          {previewMode === 'supply' && 'المورد: شركة التقنية'}
          {previewMode === 'stocktake' && 'المخزن: الرئيسي'}
        </div>
        <div style={{ textAlign: 'center', flex: 1 }}>
          رقم الأمر: 12345
        </div>
        <div style={{ textAlign: 'left', flex: 1 }}>
          الموظف: أحمد علي | التاريخ: {new Date().toLocaleDateString('ar-SA')}
        </div>
      </div>

      {/* الجدول التجريبي */}
      <table
        style={{
          width: '100%',
          borderCollapse: 'collapse',
          fontSize: layout.table.fontSize,
          marginTop: '20px',
        }}
      >
        <thead>
          <tr style={{ backgroundColor: layout.table.headerBackgroundColor }}>
            {previewData.headers.map((header, index) => (
              <th
                key={index}
                style={{
                  color: layout.table.headerTextColor,
                  padding: layout.table.cellPadding,
                  border: `1px solid ${layout.table.borderColor}`,
                  textAlign: 'center',
                }}
              >
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {previewData.data.map((row, rowIndex) => (
            <tr
              key={rowIndex}
              style={{
                backgroundColor: rowIndex % 2 === 1 ? layout.table.rowAlternateColor : 'transparent',
              }}
            >
              {row.map((cell, cellIndex) => (
                <td
                  key={cellIndex}
                  style={{
                    padding: layout.table.cellPadding,
                    border: `1px solid ${layout.table.borderColor}`,
                    textAlign: cellIndex === 0 ? 'center' : 'right',
                  }}
                >
                  {cell}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
        <tfoot>
          <tr style={{ backgroundColor: layout.table.headerBackgroundColor, opacity: 0.8 }}>
            <td
              colSpan={previewData.headers.length - 1}
              style={{
                color: layout.table.headerTextColor,
                padding: layout.table.cellPadding,
                border: `1px solid ${layout.table.borderColor}`,
                textAlign: 'center',
                fontWeight: 'bold',
              }}
            >
              المجموع ({previewData.totals.count} عنصر)
            </td>
            <td
              style={{
                color: layout.table.headerTextColor,
                padding: layout.table.cellPadding,
                border: `1px solid ${layout.table.borderColor}`,
                textAlign: 'center',
                fontWeight: 'bold',
              }}
            >
              {previewData.totals.total}
            </td>
          </tr>
        </tfoot>
      </table>

      {/* التذييل */}
      <div
        style={{
          textAlign: layout.footer.position,
          fontSize: layout.footer.fontSize,
          color: layout.footer.color,
          marginTop: layout.footer.marginTop,
          position: 'absolute',
          bottom: layout.page.marginBottom,
          left: layout.page.marginLeft,
          right: layout.page.marginRight,
        }}
      >
        {layout.footer.showDate && (
          <span>تاريخ الطباعة: {new Date().toLocaleDateString('ar-SA')}</span>
        )}
        {layout.footer.showPageNumbers && layout.footer.showDate && ' | '}
        {layout.footer.showPageNumbers && <span>صفحة 1 من 1</span>}
        {settings.footerTextAr && (
          <div style={{ marginTop: '5px' }}>{settings.footerTextAr}</div>
        )}
      </div>
    </div>
  );

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-screen">
      {/* لوحة التحكم */}
      <div className="space-y-4 overflow-y-auto">
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                تصميم التقارير والطباعة
              </CardTitle>
              <div className="flex items-center gap-2">
                <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                  {previewMode === 'sales' ? 'فاتورة مبيعات' :
                   previewMode === 'supply' ? 'أمر توريد' :
                   previewMode === 'stocktake' ? 'تقرير جرد' : 'تقرير مخصص'}
                </span>
              </div>
            </div>
            <div className="flex gap-2 pt-2">
              <Button size="sm" variant="outline" onClick={resetToDefaults}>
                <RotateCw className="h-4 w-4 ml-2" />
                إعادة تعيين
              </Button>
              <Button size="sm" variant="outline" onClick={exportLayout}>
                <Download className="h-4 w-4 ml-2" />
                تصدير
              </Button>
              <div>
                <input
                  type="file"
                  accept=".json"
                  onChange={importLayout}
                  className="hidden"
                  id="import-layout"
                />
                <Button size="sm" variant="outline" asChild>
                  <label htmlFor="import-layout" className="cursor-pointer flex items-center">
                    <Upload className="h-4 w-4 ml-2" />
                    استيراد
                  </label>
                </Button>
              </div>
              <Button size="sm" onClick={() => onSave(layout)} className="bg-green-600 hover:bg-green-700">
                <Save className="h-4 w-4 ml-2" />
                حفظ
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-6">
                <TabsTrigger value="logo" className="text-xs">
                  <FileImage className="h-4 w-4" />
                  <span className="ml-1 hidden sm:inline">شعار</span>
                </TabsTrigger>
                <TabsTrigger value="title" className="text-xs">
                  <Type className="h-4 w-4" />
                  <span className="ml-1 hidden sm:inline">عنوان</span>
                </TabsTrigger>
                <TabsTrigger value="company" className="text-xs">
                  <Settings className="h-4 w-4" />
                  <span className="ml-1 hidden sm:inline">شركة</span>
                </TabsTrigger>
                <TabsTrigger value="table" className="text-xs">
                  <Layout className="h-4 w-4" />
                  <span className="ml-1 hidden sm:inline">جدول</span>
                </TabsTrigger>
                <TabsTrigger value="footer" className="text-xs">
                  <Palette className="h-4 w-4" />
                  <span className="ml-1 hidden sm:inline">تذييل</span>
                </TabsTrigger>
                <TabsTrigger value="page" className="text-xs">
                  <Settings className="h-4 w-4" />
                  <span className="ml-1 hidden sm:inline">صفحة</span>
                </TabsTrigger>
              </TabsList>

              {/* تبويب الشعار */}
              <TabsContent value="logo" className="space-y-4">
                <h3 className="text-lg font-semibold">إعدادات الشعار</h3>
                
                <div className="space-y-2">
                  <Label>حجم الشعار: {layout.logo.size}px</Label>
                  <Slider
                    value={[layout.logo.size]}
                    onValueChange={([value]) => updateLayout('logo', { size: value })}
                    max={120}
                    min={20}
                    step={5}
                  />
                </div>

                <div className="space-y-2">
                  <Label>موقع الشعار</Label>
                  <Select
                    value={layout.logo.position}
                    onValueChange={(value) => updateLayout('logo', { position: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="left">يسار</SelectItem>
                      <SelectItem value="center">وسط</SelectItem>
                      <SelectItem value="right">يمين</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>هامش علوي: {layout.logo.marginTop}px</Label>
                    <Slider
                      value={[layout.logo.marginTop]}
                      onValueChange={([value]) => updateLayout('logo', { marginTop: value })}
                      max={50}
                      min={0}
                      step={1}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>هامش سفلي: {layout.logo.marginBottom}px</Label>
                    <Slider
                      value={[layout.logo.marginBottom]}
                      onValueChange={([value]) => updateLayout('logo', { marginBottom: value })}
                      max={50}
                      min={0}
                      step={1}
                    />
                  </div>
                </div>
              </TabsContent>

              {/* تبويب العنوان */}
              <TabsContent value="title" className="space-y-4">
                <h3 className="text-lg font-semibold">إعدادات العنوان</h3>
                
                <div className="space-y-2">
                  <Label>حجم الخط: {layout.title.fontSize}px</Label>
                  <Slider
                    value={[layout.title.fontSize]}
                    onValueChange={([value]) => updateLayout('title', { fontSize: value })}
                    max={32}
                    min={12}
                    step={1}
                  />
                </div>

                <div className="space-y-2">
                  <Label>وزن الخط</Label>
                  <Select
                    value={layout.title.fontWeight}
                    onValueChange={(value) => updateLayout('title', { fontWeight: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="normal">عادي</SelectItem>
                      <SelectItem value="bold">عريض</SelectItem>
                      <SelectItem value="bolder">عريض جداً</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>محاذاة النص</Label>
                  <Select
                    value={layout.title.position}
                    onValueChange={(value) => updateLayout('title', { position: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="left">يسار</SelectItem>
                      <SelectItem value="center">وسط</SelectItem>
                      <SelectItem value="right">يمين</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>لون النص</Label>
                  <Input
                    type="color"
                    value={layout.title.color}
                    onChange={(e) => updateLayout('title', { color: e.target.value })}
                    className="h-10"
                  />
                </div>
              </TabsContent>

              {/* تبويب معلومات الشركة */}
              <TabsContent value="company" className="space-y-4">
                <h3 className="text-lg font-semibold">إعدادات معلومات الشركة</h3>
                
                <div className="space-y-2">
                  <Label>حجم الخط: {layout.companyInfo.fontSize}px</Label>
                  <Slider
                    value={[layout.companyInfo.fontSize]}
                    onValueChange={([value]) => updateLayout('companyInfo', { fontSize: value })}
                    max={18}
                    min={8}
                    step={1}
                  />
                </div>

                <div className="space-y-2">
                  <Label>وزن الخط</Label>
                  <Select
                    value={layout.companyInfo.fontWeight}
                    onValueChange={(value) => updateLayout('companyInfo', { fontWeight: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="normal">عادي</SelectItem>
                      <SelectItem value="bold">عريض</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>محاذاة النص</Label>
                  <Select
                    value={layout.companyInfo.position}
                    onValueChange={(value) => updateLayout('companyInfo', { position: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="left">يسار</SelectItem>
                      <SelectItem value="center">وسط</SelectItem>
                      <SelectItem value="right">يمين</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>لون النص</Label>
                  <Input
                    type="color"
                    value={layout.companyInfo.color}
                    onChange={(e) => updateLayout('companyInfo', { color: e.target.value })}
                    className="h-10"
                  />
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Switch
                      checked={layout.companyInfo.showArabic}
                      onCheckedChange={(checked) => updateLayout('companyInfo', { showArabic: checked })}
                    />
                    <Label>عرض النصوص العربية</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Switch
                      checked={layout.companyInfo.showEnglish}
                      onCheckedChange={(checked) => updateLayout('companyInfo', { showEnglish: checked })}
                    />
                    <Label>عرض النصوص الإنجليزية</Label>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>هامش علوي: {layout.companyInfo.marginTop}px</Label>
                    <Slider
                      value={[layout.companyInfo.marginTop]}
                      onValueChange={([value]) => updateLayout('companyInfo', { marginTop: value })}
                      max={30}
                      min={0}
                      step={1}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>هامش سفلي: {layout.companyInfo.marginBottom}px</Label>
                    <Slider
                      value={[layout.companyInfo.marginBottom]}
                      onValueChange={([value]) => updateLayout('companyInfo', { marginBottom: value })}
                      max={30}
                      min={0}
                      step={1}
                    />
                  </div>
                </div>
              </TabsContent>

              {/* تبويب الجدول */}
              <TabsContent value="table" className="space-y-4">
                <h3 className="text-lg font-semibold">إعدادات الجدول</h3>
                
                <div className="space-y-2">
                  <Label>حجم خط الجدول: {layout.table.fontSize}px</Label>
                  <Slider
                    value={[layout.table.fontSize]}
                    onValueChange={([value]) => updateLayout('table', { fontSize: value })}
                    max={16}
                    min={8}
                    step={1}
                  />
                </div>

                <div className="space-y-2">
                  <Label>حشو الخلايا: {layout.table.cellPadding}px</Label>
                  <Slider
                    value={[layout.table.cellPadding]}
                    onValueChange={([value]) => updateLayout('table', { cellPadding: value })}
                    max={20}
                    min={2}
                    step={1}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>لون خلفية الرأس</Label>
                    <Input
                      type="color"
                      value={layout.table.headerBackgroundColor}
                      onChange={(e) => updateLayout('table', { headerBackgroundColor: e.target.value })}
                      className="h-10"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>لون نص الرأس</Label>
                    <Input
                      type="color"
                      value={layout.table.headerTextColor}
                      onChange={(e) => updateLayout('table', { headerTextColor: e.target.value })}
                      className="h-10"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>لون الصفوف المتناوبة</Label>
                    <Input
                      type="color"
                      value={layout.table.rowAlternateColor}
                      onChange={(e) => updateLayout('table', { rowAlternateColor: e.target.value })}
                      className="h-10"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>لون الحدود</Label>
                    <Input
                      type="color"
                      value={layout.table.borderColor}
                      onChange={(e) => updateLayout('table', { borderColor: e.target.value })}
                      className="h-10"
                    />
                  </div>
                </div>
              </TabsContent>

              {/* تبويب التذييل */}
              <TabsContent value="footer" className="space-y-4">
                <h3 className="text-lg font-semibold">إعدادات التذييل</h3>
                
                <div className="space-y-2">
                  <Label>حجم الخط: {layout.footer.fontSize}px</Label>
                  <Slider
                    value={[layout.footer.fontSize]}
                    onValueChange={([value]) => updateLayout('footer', { fontSize: value })}
                    max={16}
                    min={8}
                    step={1}
                  />
                </div>

                <div className="space-y-2">
                  <Label>محاذاة النص</Label>
                  <Select
                    value={layout.footer.position}
                    onValueChange={(value) => updateLayout('footer', { position: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="left">يسار</SelectItem>
                      <SelectItem value="center">وسط</SelectItem>
                      <SelectItem value="right">يمين</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Switch
                      checked={layout.footer.showPageNumbers}
                      onCheckedChange={(checked) => updateLayout('footer', { showPageNumbers: checked })}
                    />
                    <Label>عرض أرقام الصفحات</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Switch
                      checked={layout.footer.showDate}
                      onCheckedChange={(checked) => updateLayout('footer', { showDate: checked })}
                    />
                    <Label>عرض تاريخ الطباعة</Label>
                  </div>
                </div>
              </TabsContent>

              {/* تبويب الصفحة */}
              <TabsContent value="page" className="space-y-4">
                <h3 className="text-lg font-semibold">إعدادات الصفحة</h3>
                
                <div className="space-y-2">
                  <Label>اتجاه الصفحة</Label>
                  <Select
                    value={layout.page.orientation}
                    onValueChange={(value) => updateLayout('page', { orientation: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="portrait">عمودي</SelectItem>
                      <SelectItem value="landscape">أفقي</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>هامش علوي: {layout.page.marginTop}px</Label>
                    <Slider
                      value={[layout.page.marginTop]}
                      onValueChange={([value]) => updateLayout('page', { marginTop: value })}
                      max={50}
                      min={10}
                      step={1}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>هامش سفلي: {layout.page.marginBottom}px</Label>
                    <Slider
                      value={[layout.page.marginBottom]}
                      onValueChange={([value]) => updateLayout('page', { marginBottom: value })}
                      max={50}
                      min={10}
                      step={1}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>هامش يسار: {layout.page.marginLeft}px</Label>
                    <Slider
                      value={[layout.page.marginLeft]}
                      onValueChange={([value]) => updateLayout('page', { marginLeft: value })}
                      max={50}
                      min={10}
                      step={1}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>هامش يمين: {layout.page.marginRight}px</Label>
                    <Slider
                      value={[layout.page.marginRight]}
                      onValueChange={([value]) => updateLayout('page', { marginRight: value })}
                      max={50}
                      min={10}
                      step={1}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* معلومات مفيدة */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <h4 className="font-semibold text-blue-800 mb-2 flex items-center gap-2">
              <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              نصائح التصميم
            </h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• سيتم تطبيق هذا التصميم على جميع التقارير في النظام</li>
              <li>• يمكنك تصدير التصميم ومشاركته مع أنظمة أخرى</li>
              <li>• اختبر التصميم مع أنواع تقارير مختلفة قبل الحفظ</li>
              <li>• احفظ نسخة احتياطية من التصميم قبل إجراء تغييرات كبيرة</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* نافذة المعاينة */}
      <div className="border rounded-lg bg-gray-100 p-4 overflow-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Eye className="h-5 w-5" />
            معاينة مباشرة
          </h3>
          <div className="flex items-center gap-2">
            {/* أزرار تغيير نوع المعاينة */}
            <div className="flex gap-1 bg-white rounded-lg p-1 border">
              <Button
                size="sm"
                variant={previewMode === 'sales' ? 'default' : 'ghost'}
                onClick={() => setPreviewMode('sales')}
                className="text-xs px-2 py-1"
              >
                💰 مبيعات
              </Button>
              <Button
                size="sm"
                variant={previewMode === 'supply' ? 'default' : 'ghost'}
                onClick={() => setPreviewMode('supply')}
                className="text-xs px-2 py-1"
              >
                📦 توريد
              </Button>
              <Button
                size="sm"
                variant={previewMode === 'stocktake' ? 'default' : 'ghost'}
                onClick={() => setPreviewMode('stocktake')}
                className="text-xs px-2 py-1"
              >
                📊 جرد
              </Button>
              <Button
                size="sm"
                variant={previewMode === 'custom' ? 'default' : 'ghost'}
                onClick={() => setPreviewMode('custom')}
                className="text-xs px-2 py-1"
              >
                ⚙️ مخصص
              </Button>
            </div>
            <div className="text-sm text-gray-600">
              {layout.page.orientation === 'portrait' ? 'A4 عمودي' : 'A4 أفقي'}
            </div>
            {/* زر تصدير PDF */}
            <Button 
              size="sm" 
              variant="outline" 
              className="ml-2" 
              onClick={async () => {
                const { exportToPDF } = await import('@/lib/export-utils/report-helpers');
                const previewData = getPreviewData();
                const options = {
                  title: {
                    ar: previewData.title,
                    en: previewData.title === 'فاتورة بيع' ? 'Sales Invoice' :
                        previewData.title === 'أمر توريد' ? 'Supply Order' :
                        previewData.title === 'تقرير جرد المخزون' ? 'Stocktake Report' :
                        'Custom Report'
                  },
                  reportType: previewData.subtitle,
                  orderInfo: {
                    clientName: previewMode === 'sales' ? 'محمد صالح' : undefined,
                    supplierName: previewMode === 'supply' ? 'شركة التقنية' : undefined,
                    warehouseName: previewMode === 'stocktake' ? 'الرئيسي' : undefined,
                    orderNumber: '12345',
                    employeeName: 'أحمد علي',
                    date: new Date().toLocaleDateString('ar-SA'),
                  },
                  tableHeaders: previewData.headers,
                  tableData: previewData.data,
                  totals: {
                    label: `المجموع (${previewData.totals.count} عنصر)`,
                    value: previewData.totals.total
                  }
                };
                
                const fileName = `${previewData.title.replace(' ', '-')}-${new Date().getTime()}`;
                await exportToPDF(settings, options, fileName);
              }}>
              <Download className="h-4 w-4 ml-1" />
              تصدير PDF
            </Button>
          </div>
        </div>
        <div className="flex justify-center">
          <PreviewComponent />
        </div>
      </div>
    </div>
  );
}
