import { ReportGenerationOptions, generateAdvancedReport } from "./report-generator";
import { SystemSettings } from "@/lib/types";

export function exportToPDF(
  settings: SystemSettings,
  options: ReportGenerationOptions,
  fileName: string = "report.pdf"
) {
  try {
    const doc = generateAdvancedReport(settings, options);
    doc.save(fileName);
    return true;
  } catch (error) {
    console.error("خطأ في تصدير التقرير:", error);
    return false;
  }
}

// دالة مساعدة لتحويل التاريخ إلى التنسيق العربي
export function formatDate(date: Date | string): string {
  const d = new Date(date);
  return d.toLocaleDateString("ar-SA", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

// دالة لإنشاء خيارات التقرير لفاتورة المبيعات
export function createSalesInvoiceOptions(
  data: any,
  settings: SystemSettings
): ReportGenerationOptions {
  return {
    title: {
      ar: "فاتورة مبيعات",
      en: "Sales Invoice",
    },
    reportType: `فاتورة مبيعات رقم: ${data.invoiceNumber}`,
    orderInfo: {
      clientName: data.clientName,
      warehouseName: data.warehouseName,
      orderNumber: data.invoiceNumber,
      employeeName: data.employeeName,
      date: formatDate(data.date),
    },
    tableHeaders: ["الرقم", "الصنف", "الكمية", "السعر", "الإجمالي"],
    tableData: data.items.map((item: any, index: number) => [
      index + 1,
      item.name,
      item.quantity,
      item.price.toFixed(2),
      (item.quantity * item.price).toFixed(2),
    ]),
    totals: {
      label: "الإجمالي",
      value: data.total.toFixed(2),
    },
  };
}

// دالة لإنشاء خيارات التقرير لأمر التوريد
export function createSupplyOrderOptions(
  data: any,
  settings: SystemSettings
): ReportGenerationOptions {
  return {
    title: {
      ar: "أمر توريد",
      en: "Supply Order",
    },
    reportType: `أمر توريد رقم: ${data.orderNumber}`,
    orderInfo: {
      supplierName: data.supplierName,
      warehouseName: data.warehouseName,
      orderNumber: data.orderNumber,
      employeeName: data.employeeName,
      date: formatDate(data.date),
    },
    tableHeaders: ["الرقم", "الصنف", "الكمية", "التكلفة", "الإجمالي"],
    tableData: data.items.map((item: any, index: number) => [
      index + 1,
      item.name,
      item.quantity,
      item.cost.toFixed(2),
      (item.quantity * item.cost).toFixed(2),
    ]),
    totals: {
      label: "الإجمالي",
      value: data.total.toFixed(2),
    },
  };
}

// دالة لإنشاء خيارات التقرير للجرد
export function createStocktakeOptions(
  data: any,
  settings: SystemSettings
): ReportGenerationOptions {
  return {
    title: {
      ar: "تقرير الجرد",
      en: "Stocktake Report",
    },
    reportType: `تقرير جرد المخزون: ${data.warehouseName}`,
    orderInfo: {
      warehouseName: data.warehouseName,
      orderNumber: data.stocktakeNumber,
      employeeName: data.employeeName,
      date: formatDate(data.date),
    },
    tableHeaders: ["الرقم", "الصنف", "الرصيد الدفتري", "الرصيد الفعلي", "الفرق"],
    tableData: data.items.map((item: any, index: number) => [
      index + 1,
      item.name,
      item.bookQuantity,
      item.actualQuantity,
      item.actualQuantity - item.bookQuantity,
    ]),
    totals: {
      label: "إجمالي الفروقات",
      value: data.items.reduce(
        (sum: number, item: any) =>
          sum + (item.actualQuantity - item.bookQuantity),
        0
      ).toString(),
    },
  };
}
