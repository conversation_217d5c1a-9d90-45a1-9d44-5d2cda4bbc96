import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET - جلب جميع الأجهزة (بدون authentication للاختبار)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '1000');
    const search = searchParams.get('search') || '';
    
    const skip = (page - 1) * limit;
    
    // Build where clause for search
    const where = search ? {
      OR: [
        { id: { contains: search, mode: 'insensitive' as const } },
        { model: { contains: search, mode: 'insensitive' as const } },
        { storage: { contains: search, mode: 'insensitive' as const } },
        { status: { contains: search, mode: 'insensitive' as const } }
      ]
    } : {};

    const [devices, total] = await Promise.all([
      prisma.device.findMany({
        where,
        orderBy: { dateAdded: 'desc' },
        skip,
        take: limit
      }),
      prisma.device.count({ where })
    ]);

    return NextResponse.json({
      data: devices,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('خطأ في جلب الأجهزة:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الأجهزة' },
      { status: 500 }
    );
  }
}

// POST - إنشاء جهاز جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, model, status, storage, price, condition, warehouseId, supplierId } = body;

    // التحقق من البيانات المطلوبة
    if (!id || !model) {
      return NextResponse.json(
        { error: 'رقم الجهاز والموديل مطلوبان' },
        { status: 400 }
      );
    }

    // التحقق من عدم تكرار رقم الجهاز
    const existingDevice = await prisma.device.findUnique({
      where: { id }
    });

    if (existingDevice) {
      return NextResponse.json(
        { error: 'يوجد جهاز بهذا الرقم بالفعل' },
        { status: 400 }
      );
    }

    const device = await prisma.device.create({
      data: {
        id,
        model,
        status: status || 'متاح للبيع',
        storage: storage || 'N/A',
        price: price || 0,
        condition: condition || 'جديد',
        warehouseId: warehouseId || null,
        supplierId: supplierId || null,
        dateAdded: new Date().toISOString()
      }
    });

    return NextResponse.json(device, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء الجهاز:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء الجهاز' },
      { status: 500 }
    );
  }
}

// PUT - تحديث جهاز موجود
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, model, status, storage, price, condition, warehouseId, supplierId } = body;

    // التحقق من البيانات المطلوبة
    if (!id) {
      return NextResponse.json(
        { error: 'رقم الجهاز مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الجهاز
    const existingDevice = await prisma.device.findUnique({
      where: { id }
    });

    if (!existingDevice) {
      return NextResponse.json(
        { error: 'الجهاز غير موجود' },
        { status: 404 }
      );
    }

    const device = await prisma.device.update({
      where: { id },
      data: {
        model: model || existingDevice.model,
        status: status || existingDevice.status,
        storage: storage || existingDevice.storage,
        price: price !== undefined ? price : existingDevice.price,
        condition: condition || existingDevice.condition,
        warehouseId: warehouseId !== undefined ? warehouseId : existingDevice.warehouseId,
        supplierId: supplierId !== undefined ? supplierId : existingDevice.supplierId
      }
    });

    return NextResponse.json(device);
  } catch (error) {
    console.error('خطأ في تحديث الجهاز:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث الجهاز' },
      { status: 500 }
    );
  }
}

// DELETE - حذف جهاز
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'رقم الجهاز مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الجهاز
    const existingDevice = await prisma.device.findUnique({
      where: { id }
    });

    if (!existingDevice) {
      return NextResponse.json(
        { error: 'الجهاز غير موجود' },
        { status: 404 }
      );
    }

    await prisma.device.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'تم حذف الجهاز بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الجهاز:', error);
    return NextResponse.json(
      { error: 'فشل في حذف الجهاز' },
      { status: 500 }
    );
  }
}
