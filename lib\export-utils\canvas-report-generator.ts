import { jsPDF } from "jspdf";
import { SystemSettings } from "@/lib/types";

interface CanvasReportOptions {
  title: {
    ar: string;
    en: string;
  };
  reportType: string;
  orderInfo: {
    clientName?: string;
    supplierName?: string;
    warehouseName?: string;
    orderNumber: string;
    employeeName: string;
    date: string;
  };
  tableHeaders: string[];
  tableData: any[][];
  totals?: {
    label: string;
    value: string;
  };
}

export async function generateCanvasReport(
  settings: SystemSettings,
  options: CanvasReportOptions,
  fileName: string
): Promise<void> {
  try {
    // إنشاء PDF
    const pdf = new jsPDF("p", "mm", "a4");
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // إنشاء Canvas مؤقت لرسم النصوص
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    if (!ctx) throw new Error("فشل في إنشاء Canvas context");

    // إعداد Canvas
    canvas.width = 794; // عرض A4 بالبكسل
    canvas.height = 1123; // ارتفاع A4 بالبكسل
    ctx.fillStyle = "#ffffff";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    let currentY = 40;

    // رسم الشعار في المنتصف
    if (settings.logoUrl) {
      try {
        const { drawImageToCanvas } = await import('./image-utils');
        const logoSize = 60;
        await drawImageToCanvas(
          ctx,
          settings.logoUrl,
          (canvas.width - logoSize) / 2,
          currentY,
          logoSize,
          logoSize
        );
        currentY += logoSize + 20;
      } catch (error) {
        console.warn("تعذر إضافة الشعار:", error);
        // استمر بدون الشعار
        currentY += 20;
      }
    }

    // رسم اسم الشركة بخط كبير
    ctx.font = "bold 32px Arial";
    ctx.fillStyle = "#2c3e50";
    ctx.textAlign = "center";
    ctx.fillText(settings.companyNameAr, canvas.width / 2, currentY);
    currentY += 40;

    // رسم العناوين (عربي يمين وإنجليزي يسار)
    ctx.font = "bold 24px Arial";
    ctx.textAlign = "right";
    ctx.fillText(options.title.ar, canvas.width - 50, currentY);

    ctx.textAlign = "left";
    ctx.fillText(options.title.en, 50, currentY);
    currentY += 30;

    // رسم نوع التقرير مع تسطير
    ctx.font = "bold 20px Arial";
    ctx.textAlign = "center";
    ctx.fillStyle = "#34495e";
    const reportTypeWidth = ctx.measureText(options.reportType).width;
    ctx.fillText(options.reportType, canvas.width / 2, currentY);

    // رسم خط تحت نوع التقرير
    ctx.beginPath();
    ctx.moveTo((canvas.width - reportTypeWidth) / 2 - 10, currentY + 5);
    ctx.lineTo((canvas.width + reportTypeWidth) / 2 + 10, currentY + 5);
    ctx.strokeStyle = "#34495e";
    ctx.lineWidth = 1;
    ctx.stroke();
    currentY += 40;

    // رسم صف المعلومات
    ctx.font = "16px Arial";
    ctx.fillStyle = "#2c3e50";
    ctx.textAlign = "center";

    const infoRows: string[][] = [];
    let currentRow: string[] = [];

    // تجميع معلومات حسب نوع التقرير
    if (options.orderInfo.clientName) {
      currentRow.push(`العميل: ${options.orderInfo.clientName}`);
    }
    if (options.orderInfo.supplierName) {
      currentRow.push(`المورد: ${options.orderInfo.supplierName}`);
    }
    if (options.orderInfo.warehouseName) {
      currentRow.push(`المخزن: ${options.orderInfo.warehouseName}`);
    }

    if (currentRow.length > 0) {
      infoRows.push([...currentRow]);
    }

    // إضافة معلومات الطلب الأساسية
    infoRows.push([
      `رقم الطلب: ${options.orderInfo.orderNumber}`,
      `الموظف: ${options.orderInfo.employeeName}`,
      `التاريخ: ${options.orderInfo.date}`,
    ]);

    // رسم صفوف المعلومات
    infoRows.forEach((row) => {
      const rowText = row.join("   |   ");
      ctx.fillText(rowText, canvas.width / 2, currentY);
      currentY += 25;
    });
    currentY += 20;

    // رسم الجدول
    if (options.tableHeaders.length > 0 && options.tableData.length > 0) {
      const cellPadding = 10;
      const rowHeight = 30;
      const tableWidth = canvas.width - 100;
      const colWidth = tableWidth / options.tableHeaders.length;

      // رسم رأس الجدول
      ctx.fillStyle = "#34495e";
      ctx.fillRect(50, currentY, tableWidth, rowHeight);

      ctx.fillStyle = "#ffffff";
      ctx.font = "bold 16px Arial";
      ctx.textAlign = "center";

      options.tableHeaders.forEach((header, index) => {
        const x = 50 + (index * colWidth) + (colWidth / 2);
        ctx.fillText(header, x, currentY + 20);
      });

      currentY += rowHeight;

      // رسم صفوف البيانات
      ctx.fillStyle = "#2c3e50";
      ctx.font = "14px Arial";

      options.tableData.forEach((row, rowIndex) => {
        // خلفية متناوبة للصفوف
        if (rowIndex % 2 === 1) {
          ctx.fillStyle = "#f8f9fa";
          ctx.fillRect(50, currentY, tableWidth, rowHeight);
        }

        ctx.fillStyle = "#2c3e50";
        row.forEach((cell, cellIndex) => {
          const x = 50 + (cellIndex * colWidth) + (colWidth / 2);
          ctx.fillText(String(cell), x, currentY + 20);
        });

        // رسم حدود الخلايا
        ctx.strokeStyle = "#dee2e6";
        ctx.lineWidth = 0.5;
        ctx.strokeRect(50, currentY, tableWidth, rowHeight);

        currentY += rowHeight;
      });

      // إضافة المجموع
      if (options.totals) {
        ctx.fillStyle = "#34495e";
        ctx.fillRect(50, currentY, tableWidth, rowHeight);

        ctx.fillStyle = "#ffffff";
        ctx.font = "bold 16px Arial";
        ctx.textAlign = "right";
        ctx.fillText(
          `${options.totals.label}: ${options.totals.value}`,
          canvas.width - 60,
          currentY + 20
        );
      }
    }

    // إضافة التذييل
    const footerY = canvas.height - 50;
    ctx.font = "12px Arial";
    ctx.fillStyle = "#7f8c8d";
    ctx.textAlign = "center";

    if (settings.footerTextAr) {
      ctx.fillText(settings.footerTextAr, canvas.width / 2, footerY);
    }

    // تحويل Canvas إلى PDF
    try {
      // تحسين جودة الصورة باستخدام نسبة ضغط أقل
      const imgData = canvas.toDataURL("image/jpeg", 1.0);
      await new Promise<void>((resolve, reject) => {
        try {
          pdf.addImage({
            imageData: imgData,
            format: "JPEG",
            x: 0,
            y: 0,
            width: pageWidth,
            height: pageHeight,
            compression: "NONE"
          });
          resolve();
        } catch (err) {
          reject(err);
        }
      });

      // حفظ الملف
      pdf.save(`${fileName}.pdf`);
    } catch (error) {
      console.error("Error adding image to PDF:", error);
      throw new Error("فشل في تحويل المستند إلى PDF");

    // تنظيف
    canvas.remove();

    // عرض رسالة نجاح
    showNotification("تم إنشاء التقرير بنجاح!");

  } catch (error) {
    console.error("Error creating PDF with Canvas:", error);
    showNotification(
      "حدث خطأ أثناء التصدير: " + (error as Error).message,
      "error"
    );
  }
}

// وظيفة مساعدة لعرض الإشعارات
function showNotification(
  message: string,
  type: "success" | "error" = "success"
) {
  const notification = document.createElement("div");
  notification.textContent = message;
  notification.style.position = "fixed";
  notification.style.top = "20px";
  notification.style.right = "20px";
  notification.style.padding = "12px 20px";
  notification.style.background = type === "success" ? "#10B981" : "#EF4444";
  notification.style.color = "white";
  notification.style.borderRadius = "8px";
  notification.style.zIndex = "9999";
  notification.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
  notification.style.fontSize = "14px";
  notification.style.fontWeight = "500";
  notification.setAttribute("data-notification-id", "canvas-pdf-notification");

  document.body.appendChild(notification);

  setTimeout(() => {
    if (document.body.contains(notification)) {
      document.body.removeChild(notification);
    }
  }, 3000);
}
